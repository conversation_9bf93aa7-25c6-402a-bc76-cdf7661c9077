/*
*********************************************************************************************************
                                               _04_OS
    File			 : main.c
    By  			 : Muhe
    platform   : STM32F407ZG
	Data   		 : 2018/7/16
    Note       ����Ϊʱ�����ޣ�ֻʵ���˼򵥵Ķ��̣߳����ܴ��ڲ���BUG���������BUG�鷳��ϵQQ 1145474846
*********************************************************************************************************
*/
#include "User_header.h"
#include "User.h"

/*
***********************************************************************************************************

 											�����ջ������ÿ��������Ҫ����һ���ض��Ķ�ջ��

				Note:	��ջ��С��������ֲ��������ٶ�����Ƭ��rom���ޣ�����������������ջ�Ѵ�С

***********************************************************************************************************
*/
unsigned int TASK_0_STK[9000];
unsigned int TASK_1_STK[512];
//unsigned int TASK_2_STK[64];
//unsigned int TASK_3_STK[256];
unsigned int TASK_4_STK[256];
unsigned int TASK_5_STK[64];
/*
************************************************************************************************************

																����������

************************************************************************************************************
*/



void OS_Init()
{
	System_init();
	LED_Init();
	
	PS2_Keyboard_Init();

	OS_LCD_Init();
	//Touch_Init(); 
	
}

int main()
{
	
	Task_Create(User_main,&TASK_0_STK[9000-1],1);
	Task_Create(MyPs2KeyScan,&TASK_1_STK[512-1],0);
	Task_Create(LED_main,&TASK_5_STK[64-1],2);
	//Task_Create(run_direct,&TASK_4_STK[256-1],2);
	
	//OSTaskSuspend(5);    //��������
	OS_Init();
	OS_Start();
}


/************************************************* (C) COPYLEFT 2018 Muhe  *****************************************************/
