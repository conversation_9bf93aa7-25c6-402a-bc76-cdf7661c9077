/* ****************************
 * Project description:
 *
 * ADC initialization head file
 *
 * Author: ??????? -> 2019 Mao
 *
 * Creation Date: 2021/10/06 - 1
 * ****************************/

#ifndef USER_ADC_H
#define USER_ADC_H

/* ***************************** Include & Define Part     	*****************************/
#include "User_header.h"

// ADC???????
#define ADCDataLength 1024

// ADC????????

typedef struct 
{
	float irr_val[100];
	uint16_t irr_late_index;
	uint16_t irr_start_index;
	float max_value;
	float min_value;
	float direct_val;
}irr_data;

extern uint32_t ADCData[];
extern irr_data sample_data;
extern volatile uint8_t iir_filter_enabled;
extern volatile uint8_t dma_sync_flag;  // 0=关闭, 1=启用
extern uint8_t dma_stop_request;
/* ***************************** Function Declaration Part  *****************************/

void User_ADC_Init( uint8_t mode );

void User_ADC_GPIO_Init( uint8_t ch_num );	

void ADC_Mode_Independent_Init( void );
void ADC_DualMode_RegSimult_Init( void );

void ADC_DMA_Init( uint8_t mode );
void ADC_DMA_NVIC_Init( void );
void ADC_TIM3_Init( float fs );

void Get_DCVol( float vol[2] );
void Get_ACVol( float vol1_data[ADCDataLength] , float vol2_data[ADCDataLength] );
float Set_SamplingFre( float fs );
void IIR_ADC_DMA_Init( uint8_t mode );
void IIR_Filter_Config(float b0, float b1, float b2, float a1, float a2);
void IIR_Filter_Reset(void);
void IIR_Filter_Enable(uint8_t enable);
void IIR_Init();
void Start_ADC_Sampling(void);
void Stop_ADC_Sampling(void);
void run_direct(void);

#endif
